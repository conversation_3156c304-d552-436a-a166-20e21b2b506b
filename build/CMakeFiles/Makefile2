# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/MotorTest/mc02_test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/MotorTest/mc02_test/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/mc02_test.dir/all
all: cmake/stm32cubemx/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: cmake/stm32cubemx/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/mc02_test.dir/clean
clean: cmake/stm32cubemx/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory cmake/stm32cubemx

# Recursive "all" directory target.
cmake/stm32cubemx/all: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/all
.PHONY : cmake/stm32cubemx/all

# Recursive "preinstall" directory target.
cmake/stm32cubemx/preinstall:
.PHONY : cmake/stm32cubemx/preinstall

# Recursive "clean" directory target.
cmake/stm32cubemx/clean: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/clean
.PHONY : cmake/stm32cubemx/clean

#=============================================================================
# Target rules for target CMakeFiles/mc02_test.dir

# All Build rule for target.
CMakeFiles/mc02_test.dir/all: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=23,24,25,26,27,28,29,30,31,32,33,34,35,36 "Built target mc02_test"
.PHONY : CMakeFiles/mc02_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/mc02_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/MotorTest/mc02_test/build/CMakeFiles 36
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/mc02_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/MotorTest/mc02_test/build/CMakeFiles 0
.PHONY : CMakeFiles/mc02_test.dir/rule

# Convenience name for target.
mc02_test: CMakeFiles/mc02_test.dir/rule
.PHONY : mc02_test

# clean rule for target.
CMakeFiles/mc02_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/clean
.PHONY : CMakeFiles/mc02_test.dir/clean

#=============================================================================
# Target rules for target cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir

# All Build rule for target.
cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/all:
	$(MAKE) $(MAKESILENT) -f cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/build.make cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/depend
	$(MAKE) $(MAKESILENT) -f cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/build.make cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22 "Built target STM32_Drivers"
.PHONY : cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/all

# Build rule for subdir invocation for target.
cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/MotorTest/mc02_test/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/MotorTest/mc02_test/build/CMakeFiles 0
.PHONY : cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/rule

# Convenience name for target.
STM32_Drivers: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/rule
.PHONY : STM32_Drivers

# clean rule for target.
cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/clean:
	$(MAKE) $(MAKESILENT) -f cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/build.make cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/clean
.PHONY : cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

