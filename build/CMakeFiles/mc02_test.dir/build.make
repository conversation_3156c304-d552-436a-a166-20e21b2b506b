# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/MotorTest/mc02_test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/MotorTest/mc02_test/build

# Include any dependencies generated for this target.
include CMakeFiles/mc02_test.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/mc02_test.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/mc02_test.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/mc02_test.dir/flags.make

CMakeFiles/mc02_test.dir/Core/Src/main.c.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/Core/Src/main.c.obj: ../Core/Src/main.c
CMakeFiles/mc02_test.dir/Core/Src/main.c.obj: CMakeFiles/mc02_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/mc02_test.dir/Core/Src/main.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mc02_test.dir/Core/Src/main.c.obj -MF CMakeFiles/mc02_test.dir/Core/Src/main.c.obj.d -o CMakeFiles/mc02_test.dir/Core/Src/main.c.obj -c /home/<USER>/MotorTest/mc02_test/Core/Src/main.c

CMakeFiles/mc02_test.dir/Core/Src/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mc02_test.dir/Core/Src/main.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/MotorTest/mc02_test/Core/Src/main.c > CMakeFiles/mc02_test.dir/Core/Src/main.c.i

CMakeFiles/mc02_test.dir/Core/Src/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mc02_test.dir/Core/Src/main.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/MotorTest/mc02_test/Core/Src/main.c -o CMakeFiles/mc02_test.dir/Core/Src/main.c.s

CMakeFiles/mc02_test.dir/Core/Src/gpio.c.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/Core/Src/gpio.c.obj: ../Core/Src/gpio.c
CMakeFiles/mc02_test.dir/Core/Src/gpio.c.obj: CMakeFiles/mc02_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/mc02_test.dir/Core/Src/gpio.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mc02_test.dir/Core/Src/gpio.c.obj -MF CMakeFiles/mc02_test.dir/Core/Src/gpio.c.obj.d -o CMakeFiles/mc02_test.dir/Core/Src/gpio.c.obj -c /home/<USER>/MotorTest/mc02_test/Core/Src/gpio.c

CMakeFiles/mc02_test.dir/Core/Src/gpio.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mc02_test.dir/Core/Src/gpio.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/MotorTest/mc02_test/Core/Src/gpio.c > CMakeFiles/mc02_test.dir/Core/Src/gpio.c.i

CMakeFiles/mc02_test.dir/Core/Src/gpio.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mc02_test.dir/Core/Src/gpio.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/MotorTest/mc02_test/Core/Src/gpio.c -o CMakeFiles/mc02_test.dir/Core/Src/gpio.c.s

CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.obj: ../Core/Src/fdcan.c
CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.obj: CMakeFiles/mc02_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.obj -MF CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.obj.d -o CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.obj -c /home/<USER>/MotorTest/mc02_test/Core/Src/fdcan.c

CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/MotorTest/mc02_test/Core/Src/fdcan.c > CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.i

CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/MotorTest/mc02_test/Core/Src/fdcan.c -o CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.s

CMakeFiles/mc02_test.dir/Core/Src/adc.c.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/Core/Src/adc.c.obj: ../Core/Src/adc.c
CMakeFiles/mc02_test.dir/Core/Src/adc.c.obj: CMakeFiles/mc02_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/mc02_test.dir/Core/Src/adc.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mc02_test.dir/Core/Src/adc.c.obj -MF CMakeFiles/mc02_test.dir/Core/Src/adc.c.obj.d -o CMakeFiles/mc02_test.dir/Core/Src/adc.c.obj -c /home/<USER>/MotorTest/mc02_test/Core/Src/adc.c

CMakeFiles/mc02_test.dir/Core/Src/adc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mc02_test.dir/Core/Src/adc.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/MotorTest/mc02_test/Core/Src/adc.c > CMakeFiles/mc02_test.dir/Core/Src/adc.c.i

CMakeFiles/mc02_test.dir/Core/Src/adc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mc02_test.dir/Core/Src/adc.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/MotorTest/mc02_test/Core/Src/adc.c -o CMakeFiles/mc02_test.dir/Core/Src/adc.c.s

CMakeFiles/mc02_test.dir/Core/Src/dma.c.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/Core/Src/dma.c.obj: ../Core/Src/dma.c
CMakeFiles/mc02_test.dir/Core/Src/dma.c.obj: CMakeFiles/mc02_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/mc02_test.dir/Core/Src/dma.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mc02_test.dir/Core/Src/dma.c.obj -MF CMakeFiles/mc02_test.dir/Core/Src/dma.c.obj.d -o CMakeFiles/mc02_test.dir/Core/Src/dma.c.obj -c /home/<USER>/MotorTest/mc02_test/Core/Src/dma.c

CMakeFiles/mc02_test.dir/Core/Src/dma.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mc02_test.dir/Core/Src/dma.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/MotorTest/mc02_test/Core/Src/dma.c > CMakeFiles/mc02_test.dir/Core/Src/dma.c.i

CMakeFiles/mc02_test.dir/Core/Src/dma.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mc02_test.dir/Core/Src/dma.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/MotorTest/mc02_test/Core/Src/dma.c -o CMakeFiles/mc02_test.dir/Core/Src/dma.c.s

CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.obj: ../Core/Src/stm32h7xx_it.c
CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.obj: CMakeFiles/mc02_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.obj -MF CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.obj.d -o CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.obj -c /home/<USER>/MotorTest/mc02_test/Core/Src/stm32h7xx_it.c

CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/MotorTest/mc02_test/Core/Src/stm32h7xx_it.c > CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.i

CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/MotorTest/mc02_test/Core/Src/stm32h7xx_it.c -o CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.s

CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.obj: ../Core/Src/stm32h7xx_hal_msp.c
CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.obj: CMakeFiles/mc02_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.obj -MF CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.obj.d -o CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.obj -c /home/<USER>/MotorTest/mc02_test/Core/Src/stm32h7xx_hal_msp.c

CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/MotorTest/mc02_test/Core/Src/stm32h7xx_hal_msp.c > CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.i

CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/MotorTest/mc02_test/Core/Src/stm32h7xx_hal_msp.c -o CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.s

CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj: ../Core/Src/sysmem.c
CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj: CMakeFiles/mc02_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj -MF CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj.d -o CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj -c /home/<USER>/MotorTest/mc02_test/Core/Src/sysmem.c

CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/MotorTest/mc02_test/Core/Src/sysmem.c > CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.i

CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/MotorTest/mc02_test/Core/Src/sysmem.c -o CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.s

CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj: ../Core/Src/syscalls.c
CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj: CMakeFiles/mc02_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj -MF CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj.d -o CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj -c /home/<USER>/MotorTest/mc02_test/Core/Src/syscalls.c

CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/MotorTest/mc02_test/Core/Src/syscalls.c > CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.i

CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/MotorTest/mc02_test/Core/Src/syscalls.c -o CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.s

CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.obj: ../startup_stm32h723xx.s
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building ASM object CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.obj"
	/usr/bin/arm-none-eabi-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -o CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.obj -c /home/<USER>/MotorTest/mc02_test/startup_stm32h723xx.s

CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing ASM source to CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.i"
	/usr/bin/arm-none-eabi-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -E /home/<USER>/MotorTest/mc02_test/startup_stm32h723xx.s > CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.i

CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling ASM source to assembly CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.s"
	/usr/bin/arm-none-eabi-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -S /home/<USER>/MotorTest/mc02_test/startup_stm32h723xx.s -o CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.s

CMakeFiles/mc02_test.dir/User/adc_modlue.c.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/User/adc_modlue.c.obj: ../User/adc_modlue.c
CMakeFiles/mc02_test.dir/User/adc_modlue.c.obj: CMakeFiles/mc02_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/mc02_test.dir/User/adc_modlue.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mc02_test.dir/User/adc_modlue.c.obj -MF CMakeFiles/mc02_test.dir/User/adc_modlue.c.obj.d -o CMakeFiles/mc02_test.dir/User/adc_modlue.c.obj -c /home/<USER>/MotorTest/mc02_test/User/adc_modlue.c

CMakeFiles/mc02_test.dir/User/adc_modlue.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mc02_test.dir/User/adc_modlue.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/MotorTest/mc02_test/User/adc_modlue.c > CMakeFiles/mc02_test.dir/User/adc_modlue.c.i

CMakeFiles/mc02_test.dir/User/adc_modlue.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mc02_test.dir/User/adc_modlue.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/MotorTest/mc02_test/User/adc_modlue.c -o CMakeFiles/mc02_test.dir/User/adc_modlue.c.s

CMakeFiles/mc02_test.dir/User/key_bsp.c.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/User/key_bsp.c.obj: ../User/key_bsp.c
CMakeFiles/mc02_test.dir/User/key_bsp.c.obj: CMakeFiles/mc02_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/mc02_test.dir/User/key_bsp.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mc02_test.dir/User/key_bsp.c.obj -MF CMakeFiles/mc02_test.dir/User/key_bsp.c.obj.d -o CMakeFiles/mc02_test.dir/User/key_bsp.c.obj -c /home/<USER>/MotorTest/mc02_test/User/key_bsp.c

CMakeFiles/mc02_test.dir/User/key_bsp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mc02_test.dir/User/key_bsp.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/MotorTest/mc02_test/User/key_bsp.c > CMakeFiles/mc02_test.dir/User/key_bsp.c.i

CMakeFiles/mc02_test.dir/User/key_bsp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mc02_test.dir/User/key_bsp.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/MotorTest/mc02_test/User/key_bsp.c -o CMakeFiles/mc02_test.dir/User/key_bsp.c.s

CMakeFiles/mc02_test.dir/User/key_driver.c.obj: CMakeFiles/mc02_test.dir/flags.make
CMakeFiles/mc02_test.dir/User/key_driver.c.obj: ../User/key_driver.c
CMakeFiles/mc02_test.dir/User/key_driver.c.obj: CMakeFiles/mc02_test.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/mc02_test.dir/User/key_driver.c.obj"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/mc02_test.dir/User/key_driver.c.obj -MF CMakeFiles/mc02_test.dir/User/key_driver.c.obj.d -o CMakeFiles/mc02_test.dir/User/key_driver.c.obj -c /home/<USER>/MotorTest/mc02_test/User/key_driver.c

CMakeFiles/mc02_test.dir/User/key_driver.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/mc02_test.dir/User/key_driver.c.i"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/MotorTest/mc02_test/User/key_driver.c > CMakeFiles/mc02_test.dir/User/key_driver.c.i

CMakeFiles/mc02_test.dir/User/key_driver.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/mc02_test.dir/User/key_driver.c.s"
	/usr/bin/arm-none-eabi-gcc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/MotorTest/mc02_test/User/key_driver.c -o CMakeFiles/mc02_test.dir/User/key_driver.c.s

# Object files for target mc02_test
mc02_test_OBJECTS = \
"CMakeFiles/mc02_test.dir/Core/Src/main.c.obj" \
"CMakeFiles/mc02_test.dir/Core/Src/gpio.c.obj" \
"CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.obj" \
"CMakeFiles/mc02_test.dir/Core/Src/adc.c.obj" \
"CMakeFiles/mc02_test.dir/Core/Src/dma.c.obj" \
"CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.obj" \
"CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.obj" \
"CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj" \
"CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj" \
"CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.obj" \
"CMakeFiles/mc02_test.dir/User/adc_modlue.c.obj" \
"CMakeFiles/mc02_test.dir/User/key_bsp.c.obj" \
"CMakeFiles/mc02_test.dir/User/key_driver.c.obj"

# External object files for target mc02_test
mc02_test_EXTERNAL_OBJECTS = \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32h7xx.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_fdcan.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c.obj" \
"/home/<USER>/MotorTest/mc02_test/build/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c.obj"

mc02_test.elf: CMakeFiles/mc02_test.dir/Core/Src/main.c.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/Core/Src/gpio.c.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/Core/Src/adc.c.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/Core/Src/dma.c.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/User/adc_modlue.c.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/User/key_bsp.c.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/User/key_driver.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32h7xx.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_adc_ex.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_fdcan.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c.obj
mc02_test.elf: cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c.obj
mc02_test.elf: CMakeFiles/mc02_test.dir/build.make
mc02_test.elf: CMakeFiles/mc02_test.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/MotorTest/mc02_test/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Linking C executable mc02_test.elf"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/mc02_test.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/mc02_test.dir/build: mc02_test.elf
.PHONY : CMakeFiles/mc02_test.dir/build

CMakeFiles/mc02_test.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/mc02_test.dir/cmake_clean.cmake
.PHONY : CMakeFiles/mc02_test.dir/clean

CMakeFiles/mc02_test.dir/depend:
	cd /home/<USER>/MotorTest/mc02_test/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/MotorTest/mc02_test /home/<USER>/MotorTest/mc02_test /home/<USER>/MotorTest/mc02_test/build /home/<USER>/MotorTest/mc02_test/build /home/<USER>/MotorTest/mc02_test/build/CMakeFiles/mc02_test.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/mc02_test.dir/depend

