CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj: \
 /home/<USER>/MotorTest/mc02_test/Core/Src/sysmem.c \
 /usr/include/newlib/errno.h /usr/include/newlib/sys/errno.h \
 /usr/include/newlib/sys/reent.h /usr/include/newlib/_ansi.h \
 /usr/include/newlib/newlib.h /usr/include/newlib/_newlib_version.h \
 /usr/include/newlib/sys/config.h /usr/include/newlib/machine/ieeefp.h \
 /usr/include/newlib/sys/features.h \
 /usr/lib/gcc/arm-none-eabi/10.3.1/include/stddef.h \
 /usr/include/newlib/sys/_types.h /usr/include/newlib/machine/_types.h \
 /usr/include/newlib/machine/_default_types.h \
 /usr/include/newlib/sys/lock.h \
 /usr/lib/gcc/arm-none-eabi/10.3.1/include/stdint.h
