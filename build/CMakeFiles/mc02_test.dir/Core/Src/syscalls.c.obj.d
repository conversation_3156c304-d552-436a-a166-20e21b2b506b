CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj: \
 /home/<USER>/MotorTest/mc02_test/Core/Src/syscalls.c \
 /usr/include/newlib/sys/stat.h /usr/include/newlib/_ansi.h \
 /usr/include/newlib/newlib.h /usr/include/newlib/_newlib_version.h \
 /usr/include/newlib/sys/config.h /usr/include/newlib/machine/ieeefp.h \
 /usr/include/newlib/sys/features.h /usr/include/newlib/time.h \
 /usr/include/newlib/_ansi.h /usr/include/newlib/sys/cdefs.h \
 /usr/include/newlib/machine/_default_types.h \
 /usr/lib/gcc/arm-none-eabi/10.3.1/include/stddef.h \
 /usr/include/newlib/sys/reent.h /usr/include/newlib/sys/_types.h \
 /usr/include/newlib/machine/_types.h /usr/include/newlib/sys/lock.h \
 /usr/include/newlib/machine/time.h /usr/include/newlib/sys/types.h \
 /usr/include/newlib/sys/_stdint.h /usr/include/newlib/machine/endian.h \
 /usr/include/newlib/machine/_endian.h /usr/include/newlib/sys/select.h \
 /usr/include/newlib/sys/_sigset.h /usr/include/newlib/sys/_timeval.h \
 /usr/include/newlib/sys/timespec.h /usr/include/newlib/sys/_timespec.h \
 /usr/include/newlib/sys/_pthreadtypes.h /usr/include/newlib/sys/sched.h \
 /usr/include/newlib/machine/types.h /usr/include/newlib/sys/_locale.h \
 /usr/include/newlib/stdlib.h /usr/include/newlib/machine/stdlib.h \
 /usr/include/newlib/alloca.h /usr/include/newlib/errno.h \
 /usr/include/newlib/sys/errno.h /usr/include/newlib/stdio.h \
 /usr/lib/gcc/arm-none-eabi/10.3.1/include/stdarg.h \
 /usr/include/newlib/sys/stdio.h /usr/include/newlib/signal.h \
 /usr/include/newlib/sys/signal.h /usr/include/newlib/sys/time.h \
 /usr/include/newlib/machine/_time.h /usr/include/newlib/sys/times.h
