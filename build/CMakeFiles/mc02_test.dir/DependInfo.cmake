
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "ASM"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_ASM
  "/home/<USER>/MotorTest/mc02_test/startup_stm32h723xx.s" "/home/<USER>/MotorTest/mc02_test/build/CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.obj"
  )
set(CMAKE_ASM_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_ASM
  "DEBUG"
  "STM32H723xx"
  "USE_HAL_DRIVER"
  "USE_PWR_LDO_SUPPLY"
  )

# The include file search paths:
set(CMAKE_ASM_TARGET_INCLUDE_PATH
  "../User"
  "../cmake/stm32cubemx/../../Core/Inc"
  "../cmake/stm32cubemx/../../Drivers/STM32H7xx_HAL_Driver/Inc"
  "../cmake/stm32cubemx/../../Drivers/STM32H7xx_HAL_Driver/Inc/Legacy"
  "../cmake/stm32cubemx/../../Drivers/CMSIS/Device/ST/STM32H7xx/Include"
  "../cmake/stm32cubemx/../../Drivers/CMSIS/Include"
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/MotorTest/mc02_test/Core/Src/adc.c" "CMakeFiles/mc02_test.dir/Core/Src/adc.c.obj" "gcc" "CMakeFiles/mc02_test.dir/Core/Src/adc.c.obj.d"
  "/home/<USER>/MotorTest/mc02_test/Core/Src/dma.c" "CMakeFiles/mc02_test.dir/Core/Src/dma.c.obj" "gcc" "CMakeFiles/mc02_test.dir/Core/Src/dma.c.obj.d"
  "/home/<USER>/MotorTest/mc02_test/Core/Src/fdcan.c" "CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.obj" "gcc" "CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.obj.d"
  "/home/<USER>/MotorTest/mc02_test/Core/Src/gpio.c" "CMakeFiles/mc02_test.dir/Core/Src/gpio.c.obj" "gcc" "CMakeFiles/mc02_test.dir/Core/Src/gpio.c.obj.d"
  "/home/<USER>/MotorTest/mc02_test/Core/Src/main.c" "CMakeFiles/mc02_test.dir/Core/Src/main.c.obj" "gcc" "CMakeFiles/mc02_test.dir/Core/Src/main.c.obj.d"
  "/home/<USER>/MotorTest/mc02_test/Core/Src/stm32h7xx_hal_msp.c" "CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.obj" "gcc" "CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.obj.d"
  "/home/<USER>/MotorTest/mc02_test/Core/Src/stm32h7xx_it.c" "CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.obj" "gcc" "CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.obj.d"
  "/home/<USER>/MotorTest/mc02_test/Core/Src/syscalls.c" "CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj" "gcc" "CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj.d"
  "/home/<USER>/MotorTest/mc02_test/Core/Src/sysmem.c" "CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj" "gcc" "CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj.d"
  "/home/<USER>/MotorTest/mc02_test/User/adc_modlue.c" "CMakeFiles/mc02_test.dir/User/adc_modlue.c.obj" "gcc" "CMakeFiles/mc02_test.dir/User/adc_modlue.c.obj.d"
  "/home/<USER>/MotorTest/mc02_test/User/key_bsp.c" "CMakeFiles/mc02_test.dir/User/key_bsp.c.obj" "gcc" "CMakeFiles/mc02_test.dir/User/key_bsp.c.obj.d"
  "/home/<USER>/MotorTest/mc02_test/User/key_driver.c" "CMakeFiles/mc02_test.dir/User/key_driver.c.obj" "gcc" "CMakeFiles/mc02_test.dir/User/key_driver.c.obj.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
