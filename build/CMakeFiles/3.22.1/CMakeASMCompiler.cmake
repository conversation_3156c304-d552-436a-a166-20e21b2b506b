set(CMAKE_ASM_COMPILER "/usr/bin/arm-none-eabi-gcc")
set(CMAKE_ASM_COMPILER_ARG1 "")
set(CMAKE_AR "/usr/bin/arm-none-eabi-ar")
set(CMAKE_ASM_COMPILER_AR "/usr/bin/arm-none-eabi-gcc-ar")
set(CMAKE_RANLIB "/usr/bin/arm-none-eabi-ranlib")
set(CMAKE_ASM_COMPILER_RANLIB "/usr/bin/arm-none-eabi-gcc-ranlib")
set(CMAKE_LINKER "/usr/bin/arm-none-eabi-g++")
set(CMAKE_MT "")
set(CMAKE_ASM_COMPILER_LOADED 1)
set(CMAKE_ASM_COMPILER_ID "GNU")
set(CMAKE_ASM_COMPILER_VERSION "")
set(CMAKE_ASM_COMPILER_ENV_VAR "ASM")




set(CMAKE_ASM_IGNORE_EXTENSIONS h;H;o;O;obj;OBJ;def;DEF;rc;RC)
set(CMAKE_ASM_LINKER_PREFERENCE 0)


