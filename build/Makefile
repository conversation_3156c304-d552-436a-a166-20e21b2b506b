# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/MotorTest/mc02_test

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/MotorTest/mc02_test/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/MotorTest/mc02_test/build/CMakeFiles /home/<USER>/MotorTest/mc02_test/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/MotorTest/mc02_test/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named mc02_test

# Build rule for target.
mc02_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 mc02_test
.PHONY : mc02_test

# fast build rule for target.
mc02_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/build
.PHONY : mc02_test/fast

#=============================================================================
# Target rules for targets named STM32_Drivers

# Build rule for target.
STM32_Drivers: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 STM32_Drivers
.PHONY : STM32_Drivers

# fast build rule for target.
STM32_Drivers/fast:
	$(MAKE) $(MAKESILENT) -f cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/build.make cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/build
.PHONY : STM32_Drivers/fast

Core/Src/adc.obj: Core/Src/adc.c.obj
.PHONY : Core/Src/adc.obj

# target to build an object file
Core/Src/adc.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/adc.c.obj
.PHONY : Core/Src/adc.c.obj

Core/Src/adc.i: Core/Src/adc.c.i
.PHONY : Core/Src/adc.i

# target to preprocess a source file
Core/Src/adc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/adc.c.i
.PHONY : Core/Src/adc.c.i

Core/Src/adc.s: Core/Src/adc.c.s
.PHONY : Core/Src/adc.s

# target to generate assembly for a file
Core/Src/adc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/adc.c.s
.PHONY : Core/Src/adc.c.s

Core/Src/dma.obj: Core/Src/dma.c.obj
.PHONY : Core/Src/dma.obj

# target to build an object file
Core/Src/dma.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/dma.c.obj
.PHONY : Core/Src/dma.c.obj

Core/Src/dma.i: Core/Src/dma.c.i
.PHONY : Core/Src/dma.i

# target to preprocess a source file
Core/Src/dma.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/dma.c.i
.PHONY : Core/Src/dma.c.i

Core/Src/dma.s: Core/Src/dma.c.s
.PHONY : Core/Src/dma.s

# target to generate assembly for a file
Core/Src/dma.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/dma.c.s
.PHONY : Core/Src/dma.c.s

Core/Src/fdcan.obj: Core/Src/fdcan.c.obj
.PHONY : Core/Src/fdcan.obj

# target to build an object file
Core/Src/fdcan.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.obj
.PHONY : Core/Src/fdcan.c.obj

Core/Src/fdcan.i: Core/Src/fdcan.c.i
.PHONY : Core/Src/fdcan.i

# target to preprocess a source file
Core/Src/fdcan.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.i
.PHONY : Core/Src/fdcan.c.i

Core/Src/fdcan.s: Core/Src/fdcan.c.s
.PHONY : Core/Src/fdcan.s

# target to generate assembly for a file
Core/Src/fdcan.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/fdcan.c.s
.PHONY : Core/Src/fdcan.c.s

Core/Src/gpio.obj: Core/Src/gpio.c.obj
.PHONY : Core/Src/gpio.obj

# target to build an object file
Core/Src/gpio.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/gpio.c.obj
.PHONY : Core/Src/gpio.c.obj

Core/Src/gpio.i: Core/Src/gpio.c.i
.PHONY : Core/Src/gpio.i

# target to preprocess a source file
Core/Src/gpio.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/gpio.c.i
.PHONY : Core/Src/gpio.c.i

Core/Src/gpio.s: Core/Src/gpio.c.s
.PHONY : Core/Src/gpio.s

# target to generate assembly for a file
Core/Src/gpio.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/gpio.c.s
.PHONY : Core/Src/gpio.c.s

Core/Src/main.obj: Core/Src/main.c.obj
.PHONY : Core/Src/main.obj

# target to build an object file
Core/Src/main.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/main.c.obj
.PHONY : Core/Src/main.c.obj

Core/Src/main.i: Core/Src/main.c.i
.PHONY : Core/Src/main.i

# target to preprocess a source file
Core/Src/main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/main.c.i
.PHONY : Core/Src/main.c.i

Core/Src/main.s: Core/Src/main.c.s
.PHONY : Core/Src/main.s

# target to generate assembly for a file
Core/Src/main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/main.c.s
.PHONY : Core/Src/main.c.s

Core/Src/stm32h7xx_hal_msp.obj: Core/Src/stm32h7xx_hal_msp.c.obj
.PHONY : Core/Src/stm32h7xx_hal_msp.obj

# target to build an object file
Core/Src/stm32h7xx_hal_msp.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.obj
.PHONY : Core/Src/stm32h7xx_hal_msp.c.obj

Core/Src/stm32h7xx_hal_msp.i: Core/Src/stm32h7xx_hal_msp.c.i
.PHONY : Core/Src/stm32h7xx_hal_msp.i

# target to preprocess a source file
Core/Src/stm32h7xx_hal_msp.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.i
.PHONY : Core/Src/stm32h7xx_hal_msp.c.i

Core/Src/stm32h7xx_hal_msp.s: Core/Src/stm32h7xx_hal_msp.c.s
.PHONY : Core/Src/stm32h7xx_hal_msp.s

# target to generate assembly for a file
Core/Src/stm32h7xx_hal_msp.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_hal_msp.c.s
.PHONY : Core/Src/stm32h7xx_hal_msp.c.s

Core/Src/stm32h7xx_it.obj: Core/Src/stm32h7xx_it.c.obj
.PHONY : Core/Src/stm32h7xx_it.obj

# target to build an object file
Core/Src/stm32h7xx_it.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.obj
.PHONY : Core/Src/stm32h7xx_it.c.obj

Core/Src/stm32h7xx_it.i: Core/Src/stm32h7xx_it.c.i
.PHONY : Core/Src/stm32h7xx_it.i

# target to preprocess a source file
Core/Src/stm32h7xx_it.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.i
.PHONY : Core/Src/stm32h7xx_it.c.i

Core/Src/stm32h7xx_it.s: Core/Src/stm32h7xx_it.c.s
.PHONY : Core/Src/stm32h7xx_it.s

# target to generate assembly for a file
Core/Src/stm32h7xx_it.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/stm32h7xx_it.c.s
.PHONY : Core/Src/stm32h7xx_it.c.s

Core/Src/syscalls.obj: Core/Src/syscalls.c.obj
.PHONY : Core/Src/syscalls.obj

# target to build an object file
Core/Src/syscalls.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.obj
.PHONY : Core/Src/syscalls.c.obj

Core/Src/syscalls.i: Core/Src/syscalls.c.i
.PHONY : Core/Src/syscalls.i

# target to preprocess a source file
Core/Src/syscalls.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.i
.PHONY : Core/Src/syscalls.c.i

Core/Src/syscalls.s: Core/Src/syscalls.c.s
.PHONY : Core/Src/syscalls.s

# target to generate assembly for a file
Core/Src/syscalls.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/syscalls.c.s
.PHONY : Core/Src/syscalls.c.s

Core/Src/sysmem.obj: Core/Src/sysmem.c.obj
.PHONY : Core/Src/sysmem.obj

# target to build an object file
Core/Src/sysmem.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.obj
.PHONY : Core/Src/sysmem.c.obj

Core/Src/sysmem.i: Core/Src/sysmem.c.i
.PHONY : Core/Src/sysmem.i

# target to preprocess a source file
Core/Src/sysmem.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.i
.PHONY : Core/Src/sysmem.c.i

Core/Src/sysmem.s: Core/Src/sysmem.c.s
.PHONY : Core/Src/sysmem.s

# target to generate assembly for a file
Core/Src/sysmem.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/Core/Src/sysmem.c.s
.PHONY : Core/Src/sysmem.c.s

User/adc_modlue.obj: User/adc_modlue.c.obj
.PHONY : User/adc_modlue.obj

# target to build an object file
User/adc_modlue.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/User/adc_modlue.c.obj
.PHONY : User/adc_modlue.c.obj

User/adc_modlue.i: User/adc_modlue.c.i
.PHONY : User/adc_modlue.i

# target to preprocess a source file
User/adc_modlue.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/User/adc_modlue.c.i
.PHONY : User/adc_modlue.c.i

User/adc_modlue.s: User/adc_modlue.c.s
.PHONY : User/adc_modlue.s

# target to generate assembly for a file
User/adc_modlue.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/User/adc_modlue.c.s
.PHONY : User/adc_modlue.c.s

User/key_bsp.obj: User/key_bsp.c.obj
.PHONY : User/key_bsp.obj

# target to build an object file
User/key_bsp.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/User/key_bsp.c.obj
.PHONY : User/key_bsp.c.obj

User/key_bsp.i: User/key_bsp.c.i
.PHONY : User/key_bsp.i

# target to preprocess a source file
User/key_bsp.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/User/key_bsp.c.i
.PHONY : User/key_bsp.c.i

User/key_bsp.s: User/key_bsp.c.s
.PHONY : User/key_bsp.s

# target to generate assembly for a file
User/key_bsp.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/User/key_bsp.c.s
.PHONY : User/key_bsp.c.s

User/key_driver.obj: User/key_driver.c.obj
.PHONY : User/key_driver.obj

# target to build an object file
User/key_driver.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/User/key_driver.c.obj
.PHONY : User/key_driver.c.obj

User/key_driver.i: User/key_driver.c.i
.PHONY : User/key_driver.i

# target to preprocess a source file
User/key_driver.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/User/key_driver.c.i
.PHONY : User/key_driver.c.i

User/key_driver.s: User/key_driver.c.s
.PHONY : User/key_driver.s

# target to generate assembly for a file
User/key_driver.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/User/key_driver.c.s
.PHONY : User/key_driver.c.s

startup_stm32h723xx.obj: startup_stm32h723xx.s.obj
.PHONY : startup_stm32h723xx.obj

# target to build an object file
startup_stm32h723xx.s.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/mc02_test.dir/build.make CMakeFiles/mc02_test.dir/startup_stm32h723xx.s.obj
.PHONY : startup_stm32h723xx.s.obj

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... STM32_Drivers"
	@echo "... mc02_test"
	@echo "... Core/Src/adc.obj"
	@echo "... Core/Src/adc.i"
	@echo "... Core/Src/adc.s"
	@echo "... Core/Src/dma.obj"
	@echo "... Core/Src/dma.i"
	@echo "... Core/Src/dma.s"
	@echo "... Core/Src/fdcan.obj"
	@echo "... Core/Src/fdcan.i"
	@echo "... Core/Src/fdcan.s"
	@echo "... Core/Src/gpio.obj"
	@echo "... Core/Src/gpio.i"
	@echo "... Core/Src/gpio.s"
	@echo "... Core/Src/main.obj"
	@echo "... Core/Src/main.i"
	@echo "... Core/Src/main.s"
	@echo "... Core/Src/stm32h7xx_hal_msp.obj"
	@echo "... Core/Src/stm32h7xx_hal_msp.i"
	@echo "... Core/Src/stm32h7xx_hal_msp.s"
	@echo "... Core/Src/stm32h7xx_it.obj"
	@echo "... Core/Src/stm32h7xx_it.i"
	@echo "... Core/Src/stm32h7xx_it.s"
	@echo "... Core/Src/syscalls.obj"
	@echo "... Core/Src/syscalls.i"
	@echo "... Core/Src/syscalls.s"
	@echo "... Core/Src/sysmem.obj"
	@echo "... Core/Src/sysmem.i"
	@echo "... Core/Src/sysmem.s"
	@echo "... User/adc_modlue.obj"
	@echo "... User/adc_modlue.i"
	@echo "... User/adc_modlue.s"
	@echo "... User/key_bsp.obj"
	@echo "... User/key_bsp.i"
	@echo "... User/key_bsp.s"
	@echo "... User/key_driver.obj"
	@echo "... User/key_driver.i"
	@echo "... User/key_driver.s"
	@echo "... startup_stm32h723xx.obj"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

